{% extends "base.html" %}

{% block title %}لوحة الإدارة - CMSVS{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="d-flex justify-content-between align-items-center mb-5">
    <div>
        <h1 class="display-6 fw-bold text-dark mb-2">
            <i class="bi bi-speedometer2 text-primary me-3"></i>
            لوحة الإدارة
        </h1>
        <p class="text-muted mb-0">نظرة عامة على النظام والإحصائيات</p>
    </div>
    <div class="card border-0 shadow-sm">
        <div class="card-body py-3 px-4">
            <div class="d-flex align-items-center">
                <div class="avatar-sm bg-primary bg-gradient rounded-circle d-flex align-items-center justify-content-center me-3">
                    <i class="bi bi-person text-white"></i>
                </div>
                <div>
                    <h6 class="mb-0 fw-bold">مرحباً، {{ current_user.full_name }}</h6>
                    <small class="text-muted">مدير النظام</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-5 g-4">
    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-primary text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">إجمالي الطلبات</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.total }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-arrow-up me-1"></i>
                            جميع الطلبات المسجلة
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-file-earmark-text fs-3"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-warning text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">قيد المراجعة</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.pending }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-clock me-1"></i>
                            في انتظار المراجعة
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-clock fs-3"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-info text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">قيد التنفيذ</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.in_progress }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-gear me-1"></i>
                            جاري العمل عليها
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-gear fs-3"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3">
        <div class="card card-stats bg-success text-white h-100">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="text-white-50 small fw-semibold mb-2">مكتملة</div>
                        <h2 class="display-5 fw-bold mb-0">{{ request_stats.completed }}</h2>
                        <div class="small text-white-75 mt-2">
                            <i class="bi bi-check-circle me-1"></i>
                            تم إنجازها بنجاح
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <div class="avatar-lg bg-white bg-opacity-20 rounded-3 d-flex align-items-center justify-content-center">
                            <i class="bi bi-check-circle fs-3"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-5 g-4">
    <div class="col-md-4">
        <div class="card h-100 border-0">
            <div class="card-body text-center p-4">
                <div class="avatar-lg bg-primary bg-gradient rounded-3 d-flex align-items-center justify-content-center mx-auto mb-4">
                    <i class="bi bi-people fs-3 text-white"></i>
                </div>
                <h3 class="fw-bold text-dark mb-2">{{ user_stats.total }}</h3>
                <p class="text-muted mb-0 fw-semibold">إجمالي المستخدمين</p>
                <div class="mt-3">
                    <small class="text-primary fw-semibold">
                        <i class="bi bi-arrow-up me-1"></i>
                        جميع المستخدمين المسجلين
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 border-0">
            <div class="card-body text-center p-4">
                <div class="avatar-lg bg-success bg-gradient rounded-3 d-flex align-items-center justify-content-center mx-auto mb-4">
                    <i class="bi bi-person-check fs-3 text-white"></i>
                </div>
                <h3 class="fw-bold text-dark mb-2">{{ user_stats.active }}</h3>
                <p class="text-muted mb-0 fw-semibold">المستخدمون النشطون</p>
                <div class="mt-3">
                    <small class="text-success fw-semibold">
                        <i class="bi bi-check-circle me-1"></i>
                        حسابات نشطة ومفعلة
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100 border-0">
            <div class="card-body text-center p-4">
                <div class="avatar-lg bg-info bg-gradient rounded-3 d-flex align-items-center justify-content-center mx-auto mb-4">
                    <i class="bi bi-shield-check fs-3 text-white"></i>
                </div>
                <h3 class="fw-bold text-dark mb-2">{{ user_stats.admins }}</h3>
                <p class="text-muted mb-0 fw-semibold">المديرون</p>
                <div class="mt-3">
                    <small class="text-info fw-semibold">
                        <i class="bi bi-shield me-1"></i>
                        مديرو النظام
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Requests -->
<div class="card border-0">
    <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center py-4">
        <div>
            <h4 class="mb-1 fw-bold text-dark">
                <i class="bi bi-file-earmark-text text-primary me-2"></i>
                الطلبات الأخيرة
            </h4>
            <p class="text-muted mb-0 small">آخر الطلبات المقدمة في النظام</p>
        </div>
        <a href="/admin/requests" class="btn btn-primary btn-sm px-4 py-2 fw-semibold">
            <i class="bi bi-arrow-left me-2"></i>
            عرض الكل
        </a>
    </div>
    <div class="card-body p-0">
        {% if recent_requests %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="border-0 fw-semibold text-dark ps-4">رقم الطلب</th>
                        <th class="border-0 fw-semibold text-dark">العنوان</th>
                        <th class="border-0 fw-semibold text-dark">مقدم الطلب</th>
                        <th class="border-0 fw-semibold text-dark">الحالة</th>
                        <th class="border-0 fw-semibold text-dark">التاريخ</th>
                        <th class="border-0 fw-semibold text-dark pe-4">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in recent_requests %}
                    <tr class="border-0">
                        <td class="ps-4 py-3">
                            <span class="badge bg-light text-dark fw-semibold px-3 py-2">{{ req.request_number }}</span>
                        </td>
                        <td class="py-3">
                            <div class="fw-semibold text-dark">{{ req.request_title }}</div>
                        </td>
                        <td class="py-3">
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-secondary bg-gradient rounded-circle d-flex align-items-center justify-content-center me-2">
                                    <i class="bi bi-person text-white small"></i>
                                </div>
                                <span class="fw-semibold">{{ req.user.full_name }}</span>
                            </div>
                        </td>
                        <td class="py-3">
                            {% if req.status.value == 'pending' %}
                            <span class="badge bg-warning status-badge fw-semibold px-3 py-2">قيد المراجعة</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge bg-info status-badge fw-semibold px-3 py-2">قيد التنفيذ</span>
                            {% elif req.status.value == 'completed' %}
                            <span class="badge bg-success status-badge fw-semibold px-3 py-2">مكتمل</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge bg-danger status-badge fw-semibold px-3 py-2">مرفوض</span>
                            {% endif %}
                        </td>
                        <td class="py-3">
                            <span class="text-muted fw-semibold">{{ req.created_at.strftime('%Y-%m-%d') }}</span>
                        </td>
                        <td class="pe-4 py-3">
                            <div class="d-flex gap-2">
                                <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary px-3">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض
                                </a>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle px-3"
                                            data-bs-toggle="dropdown">
                                        <i class="bi bi-gear me-1"></i>
                                        إجراءات
                                    </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="in_progress">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle text-info"></i> قيد التنفيذ
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-check-circle text-success"></i> مكتمل
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-x-circle text-danger"></i> مرفوض
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <div class="avatar-lg bg-light rounded-3 d-flex align-items-center justify-content-center mx-auto mb-4">
                <i class="bi bi-file-earmark-text fs-3 text-muted"></i>
            </div>
            <h5 class="text-muted fw-semibold">لا توجد طلبات</h5>
            <p class="text-muted mb-0">لم يتم تقديم أي طلبات حتى الآن</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
