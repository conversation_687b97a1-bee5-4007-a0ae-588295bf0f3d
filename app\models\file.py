from sqlalchemy import <PERSON><PERSON>n, Inte<PERSON>, <PERSON>, DateTime, <PERSON><PERSON><PERSON>, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import uuid
import os


class File(Base):
    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    original_filename = Column(String(255), nullable=False)
    stored_filename = Column(String(255), nullable=False, unique=True)
    file_path = Column(String(500), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    file_type = Column(String(50), nullable=False)
    mime_type = Column(String(100), nullable=False)
    request_id = Column(Integer, ForeignKey("requests.id"), nullable=False)
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    request = relationship("Request", back_populates="files")

    def __repr__(self):
        return f"<File(original_filename='{self.original_filename}', stored_filename='{self.stored_filename}')>"

    @classmethod
    def generate_unique_filename(cls, original_filename: str) -> str:
        """Generate unique filename while preserving extension"""
        name, ext = os.path.splitext(original_filename)
        unique_id = str(uuid.uuid4()).replace("-", "")[:12]
        timestamp = func.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{unique_id}{ext}"

    @property
    def file_size_mb(self) -> float:
        """Get file size in MB"""
        return round(self.file_size / (1024 * 1024), 2)
