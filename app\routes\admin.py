from fastapi import APIRouter, Depends, Request, Form, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from typing import Optional
from app.database import get_db
from app.utils.auth import verify_token
from app.services.user_service import UserService
from app.services.request_service import RequestService
from app.models.user import User, UserRole
from app.models.request import RequestStatus
from app.models.activity import Activity, ActivityType

router = APIRouter(prefix="/admin")
templates = Jinja2Templates(directory="app/templates")


async def require_admin_cookie(request: Request, db: Session = Depends(get_db)) -> User:
    """Require admin role using cookie authentication"""
    token = request.cookies.get("access_token")
    if not token:
        raise HTTPException(status_code=403, detail="Not authenticated")

    # Remove 'Bearer ' prefix if present
    if token.startswith("Bearer "):
        token = token[7:]

    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=403, detail="Invalid token")

    username = payload.get("sub")
    if not username:
        raise HTTPException(status_code=403, detail="Invalid token")

    user = UserService.get_user_by_username(db, username)
    if not user:
        raise HTTPException(status_code=403, detail="User not found")

    if not user.is_active:
        raise HTTPException(status_code=403, detail="Inactive user")

    if user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    return user


@router.get("/dashboard", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Admin dashboard"""
    # Get system statistics
    request_stats = RequestService.get_request_statistics(db)
    
    # Get recent requests
    recent_requests = RequestService.get_all_requests(db, limit=10)
    
    # Get all users count
    all_users = UserService.get_all_users(db, limit=1000)
    user_count = len(all_users)
    admin_count = len([u for u in all_users if u.role == UserRole.ADMIN])
    active_users = len([u for u in all_users if u.is_active])
    
    return templates.TemplateResponse(
        "admin/dashboard.html",
        {
            "request": request,
            "current_user": current_user,
            "request_stats": request_stats,
            "recent_requests": recent_requests,
            "user_stats": {
                "total": user_count,
                "admins": admin_count,
                "active": active_users
            }
        }
    )


@router.get("/users", response_class=HTMLResponse)
async def manage_users(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Manage users page"""
    users = UserService.get_all_users(db, limit=100)
    
    return templates.TemplateResponse(
        "admin/users.html",
        {
            "request": request,
            "current_user": current_user,
            "users": users
        }
    )


@router.post("/users/{user_id}/toggle-status")
async def toggle_user_status(
    request: Request,
    user_id: int,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Toggle user active status"""
    user = UserService.get_user_by_id(db, user_id)
    if not user:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )
    
    # Don't allow deactivating self
    if user.id == current_user.id:
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "current_user": current_user,
                "users": UserService.get_all_users(db, limit=100),
                "error": "Cannot deactivate your own account"
            },
            status_code=400
        )
    
    # Toggle status
    UserService.update_user(db, user_id, is_active=not user.is_active)
    
    # Log activity
    action = "activated" if not user.is_active else "deactivated"
    UserService.log_activity(
        db=db,
        user_id=current_user.id,
        activity_type=ActivityType.PROFILE_UPDATED,
        description=f"Admin {action} user: {user.username}",
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )
    
    users = UserService.get_all_users(db, limit=100)
    return templates.TemplateResponse(
        "admin/users.html",
        {
            "request": request,
            "current_user": current_user,
            "users": users,
            "success": f"User {user.username} has been {action}"
        }
    )


@router.get("/requests", response_class=HTMLResponse)
async def manage_requests(
    request: Request,
    status: Optional[str] = None,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Manage requests page"""
    # Parse status filter
    status_filter = None
    if status:
        try:
            status_filter = RequestStatus(status)
        except ValueError:
            pass
    
    requests = RequestService.get_all_requests(db, limit=100, status=status_filter)
    
    return templates.TemplateResponse(
        "admin/requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": requests,
            "current_status": status,
            "statuses": [s.value for s in RequestStatus]
        }
    )


@router.post("/requests/{request_id}/update-status")
async def update_request_status(
    request: Request,
    request_id: int,
    status: str = Form(...),
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """Update request status"""
    try:
        new_status = RequestStatus(status)
    except ValueError:
        return templates.TemplateResponse(
            "admin/requests.html",
            {
                "request": request,
                "current_user": current_user,
                "requests": RequestService.get_all_requests(db, limit=100),
                "error": "Invalid status"
            },
            status_code=400
        )
    
    req = RequestService.update_request_status(db, request_id, new_status)
    if not req:
        return templates.TemplateResponse(
            "errors/404.html",
            {"request": request, "current_user": current_user},
            status_code=404
        )
    
    # Log activity
    UserService.log_activity(
        db=db,
        user_id=current_user.id,
        activity_type=ActivityType.REQUEST_UPDATED,
        description=f"Admin updated request {req.request_number} status to {new_status.value}",
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )
    
    requests = RequestService.get_all_requests(db, limit=100)
    return templates.TemplateResponse(
        "admin/requests.html",
        {
            "request": request,
            "current_user": current_user,
            "requests": requests,
            "success": f"Request {req.request_number} status updated to {new_status.value}"
        }
    )


@router.get("/activities", response_class=HTMLResponse)
async def view_activities(
    request: Request,
    current_user: User = Depends(require_admin_cookie),
    db: Session = Depends(get_db)
):
    """View system activities"""
    # Get recent activities from all users
    activities = db.query(Activity).order_by(Activity.created_at.desc()).limit(100).all()
    
    return templates.TemplateResponse(
        "admin/activities.html",
        {
            "request": request,
            "current_user": current_user,
            "activities": activities
        }
    )
