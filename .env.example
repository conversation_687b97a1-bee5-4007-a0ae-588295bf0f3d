# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/cmsvs_db

# Security
SECRET_KEY=your-secret-key-here-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=pdf,doc,docx,txt,jpg,jpeg,png,gif
UPLOAD_DIRECTORY=uploads

# Application Configuration
APP_NAME=CMSVS Internal System
APP_VERSION=1.0.0
DEBUG=True

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123  # Change this in production
