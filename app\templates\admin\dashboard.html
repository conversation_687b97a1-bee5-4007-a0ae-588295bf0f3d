{% extends "base.html" %}

{% block title %}لوحة الإدارة - CMSVS{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-speedometer2"></i>
        لوحة الإدارة
    </h2>
    <div class="text-muted">
        مرحباً، {{ current_user.full_name }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-stats bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ request_stats.total }}</h3>
                        <p class="card-text">إجمالي الطلبات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-text display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ request_stats.pending }}</h3>
                        <p class="card-text">قيد المراجعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ request_stats.in_progress }}</h3>
                        <p class="card-text">قيد التنفيذ</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-gear display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">{{ request_stats.completed }}</h3>
                        <p class="card-text">مكتملة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="bi bi-people display-4 text-primary"></i>
                <h4 class="mt-2">{{ user_stats.total }}</h4>
                <p class="text-muted">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="bi bi-person-check display-4 text-success"></i>
                <h4 class="mt-2">{{ user_stats.active }}</h4>
                <p class="text-muted">المستخدمون النشطون</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="bi bi-shield-check display-4 text-info"></i>
                <h4 class="mt-2">{{ user_stats.admins }}</h4>
                <p class="text-muted">المديرون</p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Requests -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-file-earmark-text"></i>
            الطلبات الأخيرة
        </h5>
        <a href="/admin/requests" class="btn btn-sm btn-outline-primary">عرض الكل</a>
    </div>
    <div class="card-body">
        {% if recent_requests %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العنوان</th>
                        <th>مقدم الطلب</th>
                        <th>الحالة</th>
                        <th>التاريخ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for req in recent_requests %}
                    <tr>
                        <td>
                            <code>{{ req.request_number }}</code>
                        </td>
                        <td>{{ req.request_title }}</td>
                        <td>{{ req.user.full_name }}</td>
                        <td>
                            {% if req.status.value == 'pending' %}
                            <span class="badge bg-warning status-badge">قيد المراجعة</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge bg-info status-badge">قيد التنفيذ</span>
                            {% elif req.status.value == 'completed' %}
                            <span class="badge bg-success status-badge">مكتمل</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge bg-danger status-badge">مرفوض</span>
                            {% endif %}
                        </td>
                        <td>{{ req.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <a href="/requests/{{ req.id }}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-eye"></i>
                            </a>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-gear"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="in_progress">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-play-circle text-info"></i> قيد التنفيذ
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-check-circle text-success"></i> مكتمل
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="d-inline">
                                            <input type="hidden" name="status" value="rejected">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-x-circle text-danger"></i> مرفوض
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="bi bi-file-earmark-text display-4 text-muted"></i>
            <p class="text-muted mt-2">لا توجد طلبات</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
