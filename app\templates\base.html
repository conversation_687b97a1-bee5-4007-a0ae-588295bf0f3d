<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CMSVS Internal System{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome as fallback -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
    
    <!-- Custom CSS - Soft UI Design -->
    <style>
        :root {
            --soft-bg: #f8f9fa;
            --soft-white: #ffffff;
            --soft-primary: #5e72e4;
            --soft-secondary: #8392ab;
            --soft-success: #2dce89;
            --soft-info: #11cdef;
            --soft-warning: #fb6340;
            --soft-danger: #f5365c;
            --soft-dark: #32325d;
            --soft-light: #e9ecef;
            --soft-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12), 0 0.125rem 0.25rem -0.0625rem rgba(20, 20, 20, 0.07);
            --soft-shadow-lg: 0 0.5rem 1rem -0.25rem rgba(20, 20, 20, 0.15), 0 0.25rem 0.5rem -0.125rem rgba(20, 20, 20, 0.1);
            --soft-shadow-inset: inset 0 1px 2px rgba(20, 20, 20, 0.1);
            --soft-border-radius: 0.75rem;
            --soft-border-radius-lg: 1rem;
            --soft-border-radius-xl: 1.5rem;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--soft-dark);
        }

        /* Navbar Soft UI */
        .navbar {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%) !important;
            box-shadow: var(--soft-shadow);
            border-radius: 0 0 var(--soft-border-radius) var(--soft-border-radius);
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.25rem;
            text-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: var(--soft-border-radius);
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        /* Sidebar Soft UI */
        .sidebar {
            min-height: calc(100vh - 56px);
            background: var(--soft-white);
            box-shadow: var(--soft-shadow);
            border-radius: var(--soft-border-radius);
            margin: 1rem 0.5rem;
            padding: 1.5rem !important;
        }

        .list-group-item {
            border: none;
            background: transparent;
            border-radius: var(--soft-border-radius) !important;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
            color: var(--soft-secondary);
        }

        .list-group-item:hover, .list-group-item.active {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
            transform: translateX(5px);
            box-shadow: var(--soft-shadow);
        }

        .list-group-item i {
            margin-left: 0.75rem;
            width: 1.25rem;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            min-height: calc(100vh - 56px);
            padding: 2rem !important;
        }

        /* Cards Soft UI */
        .card {
            border: none;
            border-radius: var(--soft-border-radius-lg);
            box-shadow: var(--soft-shadow);
            background: var(--soft-white);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--soft-shadow-lg);
        }

        .card-header {
            background: linear-gradient(135deg, var(--soft-white) 0%, #f8f9fa 100%);
            border-bottom: 1px solid rgba(0,0,0,0.05);
            border-radius: var(--soft-border-radius-lg) var(--soft-border-radius-lg) 0 0 !important;
            padding: 1.5rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Statistics Cards */
        .card-stats {
            border: none;
            position: relative;
            overflow: hidden;
        }

        .card-stats.bg-primary {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%) !important;
        }

        .card-stats.bg-success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%) !important;
        }

        .card-stats.bg-info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%) !important;
        }

        .card-stats.bg-warning {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%) !important;
        }

        .card-stats.bg-danger {
            background: linear-gradient(135deg, var(--soft-danger) 0%, #ff1744 100%) !important;
        }

        .card-stats::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        /* Buttons Soft UI */
        .btn {
            border-radius: var(--soft-border-radius);
            font-weight: 600;
            text-transform: none;
            transition: all 0.3s ease;
            border: none;
            box-shadow: var(--soft-shadow);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--soft-shadow-lg);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--soft-danger) 0%, #ff1744 100%);
        }

        .btn-outline-primary {
            border: 2px solid var(--soft-primary);
            color: var(--soft-primary);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--soft-primary);
            color: white;
        }

        /* Tables Soft UI */
        .table {
            border-radius: var(--soft-border-radius);
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--soft-light) 0%, #f8f9fa 100%);
            border: none;
            font-weight: 600;
            color: var(--soft-dark);
            padding: 1rem;
        }

        .table tbody td {
            border: none;
            padding: 1rem;
            vertical-align: middle;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: rgba(94, 114, 228, 0.05);
            transform: scale(1.01);
        }

        /* Badges Soft UI */
        .badge {
            border-radius: var(--soft-border-radius);
            font-weight: 600;
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
        }

        .bg-warning {
            background: linear-gradient(135deg, var(--soft-warning) 0%, #ff8a00 100%) !important;
        }

        .bg-success {
            background: linear-gradient(135deg, var(--soft-success) 0%, #56ca00 100%) !important;
        }

        .bg-info {
            background: linear-gradient(135deg, var(--soft-info) 0%, #1171ef 100%) !important;
        }

        .bg-danger {
            background: linear-gradient(135deg, var(--soft-danger) 0%, #ff1744 100%) !important;
        }

        /* Forms Soft UI */
        .form-control, .form-select {
            border: 2px solid rgba(0,0,0,0.1);
            border-radius: var(--soft-border-radius);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: var(--soft-white);
            box-shadow: var(--soft-shadow-inset);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--soft-primary);
            box-shadow: 0 0 0 0.2rem rgba(94, 114, 228, 0.25);
            transform: translateY(-1px);
        }

        .form-label {
            font-weight: 600;
            color: var(--soft-dark);
            margin-bottom: 0.75rem;
        }

        /* File Upload Area */
        .file-upload-area {
            border: 2px dashed rgba(94, 114, 228, 0.3);
            border-radius: var(--soft-border-radius-lg);
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, rgba(94, 114, 228, 0.05) 0%, rgba(255,255,255,0.8) 100%);
        }

        .file-upload-area:hover {
            border-color: var(--soft-primary);
            background: linear-gradient(135deg, rgba(94, 114, 228, 0.1) 0%, rgba(255,255,255,0.9) 100%);
            transform: translateY(-2px);
            box-shadow: var(--soft-shadow);
        }

        /* Alerts Soft UI */
        .alert {
            border: none;
            border-radius: var(--soft-border-radius);
            box-shadow: var(--soft-shadow);
            font-weight: 500;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(45, 206, 137, 0.1) 0%, rgba(86, 202, 0, 0.05) 100%);
            color: var(--soft-success);
            border-left: 4px solid var(--soft-success);
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(245, 54, 92, 0.1) 0%, rgba(255, 23, 68, 0.05) 100%);
            color: var(--soft-danger);
            border-left: 4px solid var(--soft-danger);
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(251, 99, 64, 0.1) 0%, rgba(255, 138, 0, 0.05) 100%);
            color: var(--soft-warning);
            border-left: 4px solid var(--soft-warning);
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(17, 205, 239, 0.1) 0%, rgba(17, 113, 239, 0.05) 100%);
            color: var(--soft-info);
            border-left: 4px solid var(--soft-info);
        }

        /* Activity Items */
        .activity-item {
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-item:hover {
            background: rgba(94, 114, 228, 0.05);
            border-radius: var(--soft-border-radius);
            padding-left: 1rem;
            padding-right: 1rem;
        }

        /* Status Badge */
        .status-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.375rem 0.75rem;
            border-radius: var(--soft-border-radius);
        }

        /* Dropdown Soft UI */
        .dropdown-menu {
            border: none;
            border-radius: var(--soft-border-radius);
            box-shadow: var(--soft-shadow-lg);
            padding: 0.5rem;
        }

        .dropdown-item {
            border-radius: var(--soft-border-radius);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, var(--soft-primary) 0%, #667eea 100%);
            color: white;
            transform: translateX(5px);
        }

        /* Animations */
        @keyframes softFadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card, .alert {
            animation: softFadeIn 0.6s ease-out;
        }

        /* Avatar Sizes */
        .avatar-sm {
            width: 2.5rem;
            height: 2.5rem;
        }

        .avatar-lg {
            width: 4rem;
            height: 4rem;
        }

        /* Enhanced Card Hover Effects */
        .card-stats:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 1rem 2rem -0.5rem rgba(20, 20, 20, 0.2), 0 0.5rem 1rem -0.25rem rgba(20, 20, 20, 0.15);
        }

        /* Text Utilities */
        .text-white-50 {
            color: rgba(255, 255, 255, 0.5) !important;
        }

        .text-white-75 {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        /* Display Utilities */
        .display-5 {
            font-size: 2.5rem;
            font-weight: 300;
            line-height: 1.2;
        }

        /* Enhanced Gradients for Better Visual Appeal */
        .bg-gradient {
            background-image: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        }

        /* Improved Spacing */
        .g-4 > * {
            padding: 1rem;
        }

        /* Enhanced Typography */
        .fw-semibold {
            font-weight: 600;
        }

        .display-6 {
            font-size: 2rem;
            font-weight: 600;
            line-height: 1.2;
        }

        /* Icon Fixes */
        .bi, .fa, .fas, .far {
            display: inline-block;
            font-style: normal;
            font-variant: normal;
            text-rendering: auto;
            line-height: 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Ensure icons have proper spacing */
        .bi, .fa, .fas, .far {
            margin-right: 0.25rem;
        }

        /* Icon size utilities */
        .fs-3 {
            font-size: 1.75rem !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                margin: 0.5rem;
                border-radius: var(--soft-border-radius);
            }

            .main-content {
                padding: 1rem !important;
            }

            .card-body {
                padding: 1rem;
            }

            .display-5 {
                font-size: 2rem;
            }

            .display-6 {
                font-size: 1.5rem;
            }
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <div class="navbar-brand-icon me-3">
                    <i class="bi bi-archive fs-4"></i>
                </div>
                <div>
                    <div class="fw-bold">CMSVS</div>
                    <small class="opacity-75">Internal System</small>
                </div>
            </a>
            
            {% if current_user %}
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.role.value == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="bi bi-speedometer2"></i> لوحة الإدارة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/users">
                            <i class="bi bi-people"></i> إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/requests">
                            <i class="bi bi-file-earmark-text"></i> إدارة الطلبات
                        </a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-house"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/requests">
                            <i class="bi bi-file-earmark-text"></i> طلباتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/requests/new">
                            <i class="bi bi-plus-circle"></i> طلب جديد
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person"></i> الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid p-0">
        <div class="row g-0">
            {% if current_user %}
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="sidebar">
                {% block sidebar %}
                <div class="list-group">
                    {% if current_user.role.value == 'admin' %}
                    <a href="/admin/dashboard" class="list-group-item list-group-item-action">
                        <i class="bi bi-speedometer2"></i> لوحة الإدارة
                    </a>
                    <a href="/admin/users" class="list-group-item list-group-item-action">
                        <i class="bi bi-people"></i> المستخدمون
                    </a>
                    <a href="/admin/requests" class="list-group-item list-group-item-action">
                        <i class="bi bi-file-earmark-text"></i> الطلبات
                    </a>
                    <a href="/admin/activities" class="list-group-item list-group-item-action">
                        <i class="bi bi-activity"></i> النشاطات
                    </a>
                    {% else %}
                    <a href="/dashboard" class="list-group-item list-group-item-action">
                        <i class="bi bi-house"></i> الرئيسية
                    </a>
                    <a href="/requests" class="list-group-item list-group-item-action">
                        <i class="bi bi-file-earmark-text"></i> طلباتي
                    </a>
                    <a href="/requests/new" class="list-group-item list-group-item-action">
                        <i class="bi bi-plus-circle"></i> طلب جديد
                    </a>
                    <a href="/profile" class="list-group-item list-group-item-action">
                        <i class="bi bi-person"></i> الملف الشخصي
                    </a>
                    {% endif %}
                </div>
                {% endblock %}
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content">
            {% else %}
            <div class="col-12">
                <div class="main-content">
            {% endif %}
                <!-- Alerts -->
                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    {{ error }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}
                
                {% if success %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i>
                    {{ success }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}
                
                <!-- Page Content -->
                {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // File upload preview
        function previewFiles(input) {
            var preview = document.getElementById('file-preview');
            preview.innerHTML = '';
            
            if (input.files) {
                Array.from(input.files).forEach(function(file, index) {
                    var div = document.createElement('div');
                    div.className = 'alert alert-info d-flex justify-content-between align-items-center';
                    div.innerHTML = `
                        <span><i class="bi bi-file-earmark"></i> ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                            <i class="bi bi-x"></i>
                        </button>
                    `;
                    preview.appendChild(div);
                });
            }
        }
        
        function removeFile(index) {
            // This is a simplified version - in a real implementation,
            // you'd need to manage the FileList properly
            var input = document.getElementById('files');
            var dt = new DataTransfer();
            var files = input.files;
            
            for (let i = 0; i < files.length; i++) {
                if (i !== index) {
                    dt.items.add(files[i]);
                }
            }
            
            input.files = dt.files;
            previewFiles(input);
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
